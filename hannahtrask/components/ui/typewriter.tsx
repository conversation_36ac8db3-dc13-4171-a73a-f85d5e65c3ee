'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

interface TypewriterProps {
  lines: string[]
  className?: string
  typingSpeed?: number
  deletingSpeed?: number
  pauseDuration?: number
  showCursor?: boolean
  cursorClassName?: string
}

export default function Typewriter({
  lines,
  className = '',
  typingSpeed = 100,
  deletingSpeed = 50,
  pauseDuration = 2000,
  showCursor = true,
  cursorClassName = '',
}: TypewriterProps) {
  const [currentLineIndex, setCurrentLineIndex] = useState(0)
  const [currentText, setCurrentText] = useState('')
  const [isTyping, setIsTyping] = useState(true)
  const [showCursorBlink, setShowCursorBlink] = useState(true)

  useEffect(() => {
    if (lines.length === 0) return

    const currentLine = lines[currentLineIndex]
    
    let timeout: NodeJS.Timeout

    if (isTyping) {
      // Typing phase
      if (currentText.length < currentLine.length) {
        timeout = setTimeout(() => {
          setCurrentText(currentLine.slice(0, currentText.length + 1))
        }, typingSpeed)
      } else {
        // Finished typing current line, pause before moving to next
        timeout = setTimeout(() => {
          if (currentLineIndex < lines.length - 1) {
            setIsTyping(false)
          }
        }, pauseDuration)
      }
    } else {
      // Deleting phase
      if (currentText.length > 0) {
        timeout = setTimeout(() => {
          setCurrentText(currentText.slice(0, -1))
        }, deletingSpeed)
      } else {
        // Finished deleting, move to next line
        setCurrentLineIndex((prev) => (prev + 1) % lines.length)
        setIsTyping(true)
      }
    }

    return () => clearTimeout(timeout)
  }, [currentText, currentLineIndex, isTyping, lines, typingSpeed, deletingSpeed, pauseDuration])

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className={className}
    >
      <span>{currentText}</span>
      {showCursor && (
        <span
          className={`inline-block w-0.5 bg-current ml-1 ${
            showCursorBlink ? 'animate-pulse' : ''
          } ${cursorClassName}`}
          style={{ height: '1em' }}
        >
          |
        </span>
      )}
    </motion.div>
  )
}
